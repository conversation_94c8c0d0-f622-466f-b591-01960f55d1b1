@using Bell.ca.Models
@using Bell.ca.Framework.Common.Utility
@using Bell.ca.Shop.Services.WirelineContract
@using Bell.ca.Portal.ViewModels.RSX
@using Bell.ca.Models.TVUB
@using Bell.ca.Models.OrderNow;
@using Bell.ca.Framework.Targeting;
@using Bell.ca.Models.Services;
@using Bell.ca.Models.ServiceAvailability
@using Bell.ca.Portal.Helpers;
@using System.Globalization
@using System.Text.RegularExpressions;
@using Bell.ca.Models.Common
@using Bell.ca.Ecommerce.Entities
@using Bell.ca.Framework.Common.Helpers
@using tvub = Bell.ca.Models.TVUB
@using System.Text;


@{
    var pageContext = Bell.ca.Framework.Common.PageContext.CurrentPageContext;
    UserProfileHelper profileService = new UserProfileHelper();
    bool isEmptyBUP = profileService.IsEmptyBUP();
    bool isEmptyBUPEnabled = BRSEshopUtility.DisplayToggledNodes(Bell.ca.Framework.Common.Constants.Toggles.IsEmptyBUPEnabled);
    Bell.ca.Framework.Common.LocalizedProvinces currentProvince = ((Bell.ca.Framework.Common.PageContext)HttpContext.Current.Items[Bell.ca.Framework.Common.Constants.CacheKeys.PageContext]).Page.Province;
    Bell.ca.Framework.Common.Language currentLanguage = ((Bell.ca.Framework.Common.PageContext)HttpContext.Current.Items[Bell.ca.Framework.Common.Constants.CacheKeys.PageContext]).Page.Language;
    Bell.ca.Framework.Targeting.TargetData targetData = new TargetData
    bool isStreamingEnabled = BRSEshopUtility.DisplayToggledNodes(Bell.ca.Framework.Common.Constants.ToggleKey.Streaming_Toggle);
    {
        Province = currentProvince,
        Language = currentLanguage
    };
    string RB_BYO = "/Bundles/build_your_own";
    string RB_BunGlobal = "/Bundles/global";
    ServiceAvailabilitySession session = System.Web.HttpContext.Current.Session[SessionKey.ServiceAvailability] as ServiceAvailabilitySession;
    var viewModel = Bell.ca.Portal.Helpers.Helper.populateCheckAvailabilityViewModel();
    MiniServiceAvailability serviceAvailabilityModel = Bell.ca.Portal.Helpers.BellBundlesHelper.GetMiniAvailabilityData();

    string prequalModifier = string.Empty;

    var isDSLAvailable = false;
    var isIPTVAvailable = false;
    var isDTHAvailable = false;
    var isWLAvailable = false;
    var isOTTAvailable = false;
    var isInternetAvailable = false;
    var isWHI = false;
    var isSTRM = true;
    bool prequal = true;
    string isCableInternetAddress = "False";

    var NetworkType = string.Empty;

    if (serviceAvailabilityModel != null && serviceAvailabilityModel.IsVerified != null)
    {
        prequal = (serviceAvailabilityModel.IsVerified == "t") ? false : true;
    }

    if (prequal)
    {
        isDSLAvailable = false;
        isIPTVAvailable = false;
        isDTHAvailable = false;
        isWLAvailable = false;
        isOTTAvailable = false;
        isInternetAvailable = false;
    }
    else
    {
        isWLAvailable = serviceAvailabilityModel.IsHomePhoneAvailable == "t";
        isDSLAvailable = serviceAvailabilityModel.IsInternetAvailable == "t";
        isIPTVAvailable = serviceAvailabilityModel.IsFibeAvailable == "t";
        isDTHAvailable = serviceAvailabilityModel.IsSatelliteAvailable == "t";
        isOTTAvailable = serviceAvailabilityModel.IsOTTAvailable == "t";
        NetworkType = Bell.ca.Portal.Helpers.BellBundlesHelper.GetQualNetworkType();
    }
    if (!string.IsNullOrEmpty(NetworkType) && !NetworkType.Contains("WTTH") && (isDSLAvailable || isIPTVAvailable))
    {
        isInternetAvailable = true;
    }

    if (!string.IsNullOrEmpty(NetworkType) && NetworkType.Contains("WTTH"))
    {
        isWHI = true;
        isInternetAvailable = false;
    }
    if (serviceAvailabilityModel != null && serviceAvailabilityModel.CxpPAProperties != null)
    {
        isCableInternetAddress = serviceAvailabilityModel.CxpPAProperties.IsCableInternetAddress.ToString();
    }
    if (isCableInternetAddress == "True" && !prequal)
    {
        isWHI = false;
        isInternetAvailable = true;
    }
    const string resourceCentralQual = "/BRSeShop/qualification/central";
    bool isEn = Bell.ca.Framework.Common.PageContext.CurrentPageContext.Page.Language.Equals(Bell.ca.Framework.Common.Language.English);
    //var urlRefer = HttpContext.Current.Request.RawUrl;
    string myBellHost = isEn ? "{MyBellHost_EN}" : "{MyBellHost_FR}";
    string myBellLink = Bell.ca.Framework.Common.Helpers.UrlHelper.ReplaceHosts(myBellHost);
    //string bellcahost = HttpContext.Current.Request.Url.Authority;
    string add_Existing_link = ResourceHelper.GetResourceString(resourceCentralQual, "dummy_flow_Login_url").Replace(myBellHost, myBellLink) + HttpContext.Current.Request.Url.Authority + ResourceHelper.GetResourceString(resourceCentralQual, "dummy_flow_url") + "%3FUrlReferrer=" + HttpContext.Current.Request.RawUrl;

    bool IsMobilityInternetBuyDeviceEnabled = BRSEshopUtility.DisplayToggledNodes("IsMobilityInternetBuyDeviceEnabled");
    bool IsMobilityInternetBuyFlowEnabled = BRSEshopUtility.DisplayToggledNodes("IsMobilityInternetBuyFlowEnabled");

    bool WirelineContractsEnable = BRSEshopUtility.GetConfig(Bell.ca.Framework.Common.Constants.Toggles.WirelineContracts_P1_5_Enable,
    Bell.ca.Framework.Common.Constants.Toggles.OnOffRSXToggleSettingsFile) == "ON" ? true : false;

    const string resourceCentralQualStreaming = "/BRSeShop/qualification/central_streaming_tv";
    bool isTVStreamingEnabled = BRSEshopUtility.DisplayToggledNodes(Bell.ca.Framework.Common.Constants.ToggleKey.Streaming_Toggle); @*put the toggle values here*@

}

<style>

    .graphical_ctrl input:focus ~ .tile-checkbox-wrap-master .tile-checkbox-wrap .ctrl_element {
        box-shadow: 0 0 0.2rem 0.2rem rgb(108 182 219 / 100%);
        outline: 1px #4d90fe;
    }

    .information-byod {
        padding: 20px 20px 20px 55px;
        margin: 30px auto 0;
        position: relative;
    }

        .information-byod .icon {
            position: absolute;
            left: 30px;
        }

    .tile--mobility .icon-byod {
        fill: #00549a;
        width: 42px;
        height: 42px;
    }

    .emptybup-build-your-bundle-button {
    }

    .emptybup-disabled {
        opacity: 0.65;
    }

    @@media(min-width: 768px) {

        .tile--mobility .icon-byod {
            fill: #00549a;
            width: 42px;
            height: 42px;
        }


        .information-byod {
            max-width: fit-content;
        }

        .information-byod {
            padding: 30px 30px 30px 55px;
        }
    }

    @@media(min-width: 992px) {
        .tile--mobility .icon-byod {
            width: 60px;
            height: 60px;
        }
    }
</style>

 @if (isStreamingEnabled)
 {

    <link rel="stylesheet" type="text/css" href="/Styles/BRF3/content/css/katsumi-shop.css">
    <link rel="stylesheet" type="text/css" href="/Styles/BRF3/content/css/katsumi-shop-icons.css">
 }

<!--(@RB_BYO, "Mob_Only_URL")-->
<input type="hidden" id="MobOnlyRedirect" value="@Html.Raw(ResourceHelper.GetResourceString(RB_BYO, "Mob_Only_URL"))" />
<input id="isPostQual" type="hidden" value="false" />
<input type="hidden" id="InternetAndMobilityRedirect" value="@Html.Raw(ResourceHelper.GetResourceString(RB_BYO, "Internet_and_Mob_URL"))" />
<input type="hidden" id="IsMobilityInternetBuyDeviceEnabled" value="@IsMobilityInternetBuyDeviceEnabled.ToString().ToLower()" />
<input type="hidden" id="IsMobilityInternetBuyFlowEnabled" value="@IsMobilityInternetBuyFlowEnabled.ToString().ToLower()" />
<input id="isCableInternetAddress" type="hidden" value="@isCableInternetAddress" />
@if (WirelineContractsEnable)
{
    WirelineContractService wcService = new WirelineContractService();
    ContractFilter contractFilter = wcService.PopulateContractFilters(targetData.Language.CultureCode, targetData.Province.TwoCharacterName);
    Bell.ca.Shop.Services.WirelineContract.ContractTypes[] contractTypes = wcService.ContractReferenceData(contractFilter, targetData.Language.CultureCode, targetData.Province.TwoCharacterName);
    if (contractTypes != null && contractTypes.Count() > 0)
    {
        if (contractTypes.FirstOrDefault().durationMonths == Bell.ca.Models.BRSeShop.Constants.Months_24)
        {
            <input id="fetchWLContractType" type="hidden" value="@Bell.ca.Models.BRSeShop.WireLineContractTerm.TWO_YR_TERM" />
        }
        else if (contractTypes.FirstOrDefault().durationMonths == Bell.ca.Models.BRSeShop.Constants.Months_12)
        {
            <input id="fetchWLContractType" type="hidden" value="@Bell.ca.Models.BRSeShop.WireLineContractTerm.ONE_YR_TERM" />
        }
    }
}
<div class="margin-b-30">
    <div class="tile-row byo @(prequal?"pre-qual":"") tv-streaming-byo container" id="NoncableInternetBYO">
        <div class="tile-wrap">
            @if (isInternetAvailable || prequal)
            {
                <label class="tile">
                    <span class="tile-content">
                        <span class="tile-body">
                            <span class="tile-control graphical_ctrl pointer graphical_ctrl_checkbox">
                                @if (Bell.ca.Framework.Common.Utility.BRSEshopUtility.DisplayRSXToggledNodes("BrsEshopBuyFlowAutomation", false))
                                {
                                    <input id="internet" type="checkbox" class="change-order-behaviour lob-checkbox" onclick="onAllLobCheckBoxClick(this, 'SHOP_INTERNET');" name="checkbox" @(prequal ? "disabled='disabled'" : "")>
                                }
                                else
                                {
                                    <input id="internet" type="checkbox" class="change-order-behaviour lob-checkbox" name="checkbox" @(prequal ? "disabled='disabled'" : "")>
                                }
                                <div class="tile-checkbox-wrap-master">
                                    <div class="tile-checkbox-wrap">
                                        <span class="ctrl_element"></span>
                                        <span class="icon-wrap">
                                            <i class="@(isTVStreamingEnabled ? "icon-katsumi icon-Internet-lob" : "icon icon-laptop_bl_wot")"></i>
                                        </span>
                                        <span class="tile-title eshop-title">
                                            <!--(@RB_BunGlobal, "LOB_Internet")-->
                                            @Html.Raw(ResourceHelper.GetResourceString(RB_BunGlobal, "LOB_Internet"))

                                        </span>
                                    </div>
                                </div>
                                <span class="tile-outline"></span>
                            </span>
                        </span>
                    </span>
                </label>
            }
            @if (isWHI)
            {
                <label class="tile">
                    <span class="tile-content">
                        <span class="tile-body">
                            <span class="tile-control graphical_ctrl pointer graphical_ctrl_checkbox">
                                <input id="internet" class="change-order-behaviour whi-order lob-checkbox" type="checkbox" onclick="onAllLobCheckBoxClick(this, 'SHOP_INTERNET');" name="checkbox" @(prequal ? "disabled='disabled'" : "")>
                                <div class="tile-checkbox-wrap-master">
                                    <div class="tile-checkbox-wrap">
                                        <span class="ctrl_element"></span>
                                        <span class="icon-wrap">
                                            <i class="@(isTVStreamingEnabled ? "icon-katsumi icon-Internet-lob" : "icon icon-laptop_bl_wot")"></i>
                                        </span>
                                        <span class="tile-title eshop-title">
                                            <!--(@RB_BunGlobal, "LOB_WHI")-->
                                            @Html.Raw(ResourceHelper.GetResourceString(RB_BunGlobal, "LOB_WHI"))
                                        </span>
                                    </div>
                                </div>
                                <span class="tile-outline"></span>
                            </span>
                        </span>
                    </span>
                </label>
            }

            @if ((isIPTVAvailable || isDTHAvailable) || prequal)
            {
                string LOB = ResourceHelper.GetResourceString(RB_BunGlobal, "LOB_TV");
                string id = string.Empty;
                string tvLOB = LOB;
                if (isIPTVAvailable)
                {
                    LOB = ResourceHelper.GetResourceString(RB_BunGlobal, "LOB_FibeTV");
                    id = "FibeTV";
                    tvLOB = "FIBE_TV";
                }
                else if (isDTHAvailable)
                {
                    LOB = ResourceHelper.GetResourceString(RB_BunGlobal, "LOB_SatTV");
                    id = "SatTV";
                    tvLOB = "SATELLITE_TV";
                }
                <label class="tile">
                    <span class="tile-content">
                        <span class="tile-body">
                            <span class="tile-control graphical_ctrl pointer graphical_ctrl_checkbox">
                                @if (Bell.ca.Framework.Common.Utility.BRSEshopUtility.DisplayRSXToggledNodes("BrsEshopBuyFlowAutomation", false))
                                {
                                    <input id="@id" class="change-order-behaviour lob-checkbox" type="checkbox" onclick="onAllLobCheckBoxClick(this, '@tvLOB');" name="checkbox" @(prequal ? "disabled='disabled'" : "")>
                                }
                                else
                                {
                                    <input id="@id" class="change-order-behaviour lob-checkbox" type="checkbox" name="checkbox" @(prequal ? "disabled='disabled'" : "")>
                                }
                                <div class="tile-checkbox-wrap-master">
                                    <div class="tile-checkbox-wrap">
                                        <span class="ctrl_element"></span>
                                        <span class="icon-wrap">
                                            <i class="@(isTVStreamingEnabled ? "icon-katsumi icon-tv-lob" : "icon icon-pay_per_view_bl_wot")"></i>
                                        </span>
                                        <span class="tile-title eshop-title">@LOB</span>
                                    </div>
                                </div>
                                <span class="tile-outline"></span>
                            </span>
                        </span>
                    </span>
                </label>
            }
            @* @if(isTVStreamingEnabled || (isSTRM && prequal)) removing the isSTRM and prequal temp *@
            @if(isTVStreamingEnabled) { 
                <label class="tile">
                    <span class="tile-content">
                        <span class="tile-body">
                            <span class="tile-control graphical_ctrl pointer graphical_ctrl_checkbox">
                                @if (Bell.ca.Framework.Common.Utility.BRSEshopUtility.DisplayRSXToggledNodes("Streaming_Toggle", false))
                                {
                                    <input id="STRM" class="change-order-behaviour lob-checkbox" type="checkbox" onclick="onAllLobCheckBoxClick(this, 'SUBSCRIPTIONS');" name="checkbox" @(prequal ? "disabled='disabled'" : "")>
                                }
                                else
                                {
                                    <input id="STRM" class="change-order-behaviour lob-checkbox" type="checkbox" name="checkbox" @(prequal ? "disabled='disabled'" : "")>
                                }
                                <div class="tile-checkbox-wrap-master">
                                    <div class="tile-checkbox-wrap">
                                        <span class="ctrl_element"></span>
                                        <span class="icon-wrap">
                                            <span class="icon-katsumi icon-Streaming-lob"></span>
                                        </span>
                                        <span class="tile-title eshop-title">@ResourceHelper.GetResourceString(resourceCentralQualStreaming, "SUBSCRIPTIONS")</span>
                                    </div>
                                </div>
                                <span class="tile-outline"></span>
                            </span>
                        </span>
                    </span>
                </label>
            }

            @if (isWLAvailable || prequal)
            {
                <label class="tile">
                    <span class="tile-content">
                        <span class="tile-body">
                            <span class="tile-control graphical_ctrl pointer graphical_ctrl_checkbox">
                                @if (Bell.ca.Framework.Common.Utility.BRSEshopUtility.DisplayRSXToggledNodes("BrsEshopBuyFlowAutomation", false))
                                {
                                    <input id="homephone" class="change-order-behaviour lob-checkbox" type="checkbox" onclick="onAllLobCheckBoxClick(this, 'SHOP_HOMEPHONE');" name="checkbox" @(prequal ? "disabled='disabled'" : "")>
                                }
                                else
                                {
                                    <input id="homephone" class="change-order-behaviour lob-checkbox" type="checkbox" name="checkbox" @(prequal ? "disabled='disabled'" : "")>
                                }
                                <div class="tile-checkbox-wrap-master">
                                    <div class="tile-checkbox-wrap">
                                        <span class="ctrl_element"></span>
                                        <span class="icon-wrap">
                                            <i class="@(isTVStreamingEnabled ? "icon-katsumi icon-HomePhone-lob" : "icon icon-home_wireless_phone_bl_wot")"></i>
                                        </span>
                                        <span class="tile-title eshop-title">
                                            <!--(@RB_BunGlobal, "LOB_HP")-->

                                            @Html.Raw(ResourceHelper.GetResourceString(RB_BunGlobal, "LOB_HP"))
                                        </span>
                                    </div>
                                </div>
                                <span class="tile-outline"></span>
                            </span>
                        </span>
                    </span>
                </label>
            }
            <label class="tile tile--mobility">
                <span class="tile-content">
                    <span class="tile-body">
                        <span class="tile-control graphical_ctrl pointer graphical_ctrl_checkbox">
                            <input id="mobility" class="change-order-behaviour lob-checkbox" type="checkbox" name="checkbox" @(prequal ? "disabled='disabled'" : "")>
                            <div class="tile-checkbox-wrap-master">
                                <div class="tile-checkbox-wrap">
                                    <span class="ctrl_element"></span>
                                    <span class="icon-wrap">
                                        <i class="@(prequal || !IsMobilityInternetBuyDeviceEnabled ? "" : "hide") @(IsMobilityInternetBuyDeviceEnabled ? "icon-buy-device" : "" ) @(isTVStreamingEnabled ? "icon-katsumi icon-Mobility-lob" : "icon icon-mobile__phone_bl_wot")"></i>
                                        <svg class="@(IsMobilityInternetBuyDeviceEnabled && !prequal ? "" : "hide")  @(IsMobilityInternetBuyDeviceEnabled ? "icon-byod" : "" )" focusable="false" tabindex="-1"><use xlink:href="#icon-mobility-byop" /></svg>
                                    </span>
                                    <div class="tile-title eshop-title">
                                        <!--(@RB_BunGlobal, "LOB_Mobility")-->
                                        @Html.Raw(ResourceHelper.GetResourceString(RB_BunGlobal, "LOB_Mobility"))
                                        <br>
                                        @if (IsMobilityInternetBuyDeviceEnabled)
                                        {
                                            <span class="tile-subtext text-byod @(IsMobilityInternetBuyDeviceEnabled ? "hide" : "")">@Html.Raw(ResourceHelper.GetResourceString(RB_BunGlobal, "Bring_your_own_phone"))</span>
                                        }
                                    </div>
                                </div>
                            </div>
                            <span class="tile-outline"></span>
                        </span>
                    </span>
                </span>
            </label>

            <label class="tile">
                <span class="tile-content">
                    <span class="tile-body">
                        <span class="tile-control graphical_ctrl pointer graphical_ctrl_checkbox">
                            <input id="smarthome" class="change-order-behaviour call-to-order-lob lob-checkbox" type="checkbox" name="checkbox" @(prequal ? "disabled='disabled'" : "")>
                            <div class="tile-checkbox-wrap-master">
                                <div class="tile-checkbox-wrap">
                                    <span class="ctrl_element"></span>
                                    <span class="icon-wrap">
                                        <i class="@(isTVStreamingEnabled ? "icon-katsumi icon-SmartHome-lob" : "icon icon-home_protection_services")"></i>
                                    </span>
                                    <span class="tile-title eshop-title">
                                        <!--(@RB_BunGlobal, "LOB_SmartHome")-->
                                        @Html.Raw(ResourceHelper.GetResourceString(RB_BunGlobal, "LOB_SmartHome"))
                                    </span>
                                </div>
                            </div>
                            <span class="tile-outline"></span>
                        </span>
                    </span>
                </span>
            </label>
        </div>

        @if (IsMobilityInternetBuyDeviceEnabled)
        {
            <div class="box-round bgGray19 information-byod  hide">
                <span class="icon icon-info txtBlue" aria-hidden="true"></span>
                <p class="no-margin txtBlack2 txtBold pad-b-5">@ResourceHelper.GetResourceString(RB_BYO, "BYOD_info_title")</p>
                <p class="no-margin">@ResourceHelper.GetResourceString(RB_BYO, "BYOD_info_text") <a href="@ResourceHelper.GetResourceString(RB_BYO, "BYOD_info_link_url")" class="txtUnderline" aria-label="" role="button">@ResourceHelper.GetResourceString(RB_BYO, "BYOD_info_link")</a></p>
            </div>
        }

         @if(isSTRM && isTVStreamingEnabled) {
            <div id="tv-streaming-only-selected-message" class="margin-h-auto margin-t-45 max-width-700 txtCenter" style="display:none;">@ResourceHelper.GetResourceString(resourceCentralQualStreaming, "subscriptions_desc")</div>
         }

        <div class='tile-row-link @(prequal ? "hide" : "" )'>
            <div class="build-bundle-js">
                @if (isEmptyBUPEnabled && isEmptyBUP)
                {
                    <a id="build-your-bundle-button-emptybup"
                       data-trackingCode="@ResourceHelper.GetResourceString("/bundles/ordernow_trackingcodes", "trackingcode_BYOB")"
                       class="btn btn-default emptybup-disabled"
                       href="javascript:void(0)"
                       disabled>
                        @Html.Raw(ResourceHelper.GetResourceString(RB_BYO, "BYO_Button"))
                    </a>
                }
                else
                {
                    <button id="build-your-bundle-button"
                            type="button"
                            class="btn btn-default"
                            data-trackingCode="@ResourceHelper.GetResourceString("/bundles/ordernow_trackingcodes", "trackingcode_BYOB")"
                            onclick="openLightBox('modal_bundles_pkg_select_btn_buildYourOwn');return false;"
                            disabled>
                        <!--(@RB_BYO, "BYO_Button")-->
                        @Html.Raw(ResourceHelper.GetResourceString(RB_BYO, "BYO_Button"))
                    </button>
                }
                <a id="build-your-bundle-mobility-button" style="display:none" class="btn btn-default" href="@ResourceHelper.GetResourceString(RB_BYO, "Mob_Only_URL")">
                    @Html.Raw(ResourceHelper.GetResourceString(RB_BYO, "BYO_Button"))
                </a>
            </div>
            <button id="build-your-bundle-button-Cable-Internet" style="display:none"
                    type="button"
                    class="btn btn-default"
                    data-trackingCode="@ResourceHelper.GetResourceString("/bundles/ordernow_trackingcodes", "trackingcode_BYOB")"
                    onclick="BuildYourOwn.CableInternetBuildYourOwn(this);return false;">
                <!--(@RB_BYO, "BYO_Button")-->
                @Html.Raw(ResourceHelper.GetResourceString(RB_BYO, "BYO_Button"))
            </button>
            <div class="smart-home-block-js" style="display:none">
                <p class="margin-b-15">
                    <!--(@RB_BYO, "Order_By_Number_Message")-->
                    @Html.Raw(ResourceHelper.GetResourceString(RB_BYO, "Order_By_Number_Message"))
                </p>
                <div>
                    <i class="icon icon-small_icon_call txtSize30 txtBlue  pad-r-15"></i>
                    <span class="txtBlue txtBold margin-b-5">
                        <!--(@RB_BYO, "Order_By_Number")-->
                        <a href='tel:@Html.Raw(ResourceHelper.GetResourceString(RB_BYO, "Order_By_Number"))' class="hide-desktop inline-block-xs txtUnderline txtDecorationNoneHover">
                            @Html.Raw(ResourceHelper.GetResourceString(RB_BYO, "Order_By_Number"))
                        </a>
                        <span class="hidden-xs">

                            @Html.Raw(ResourceHelper.GetResourceString(RB_BYO, "Order_By_Number"))
                        </span>
                    </span>
                </div>
            </div>
            <div class="whi-block-js" style="display:none">
                <p class="margin-b-15">
                    <!--(@RB_BYO, "Order_By_Number_Message")-->
                    @*@Html.Raw(ResourceHelper.GetResourceString(RB_BYO, "Order_By_Number_Message"))*@
                </p>
                <div>
                    @*<i class="icon icon-small_icon_call txtSize30 txtBlue  pad-r-15"></i>*@
                    <span class="txtBlue txtBold margin-b-5">

                        <!--(@RB_BYO, "Order_By_Number")-->
                        @*<a href='tel:@Html.Raw(ResourceHelper.GetResourceString(RB_BYO, "Order_By_Number"))' class="hide-desktop inline-block-xs txtUnderline txtDecorationNoneHover">
                                @Html.Raw(ResourceHelper.GetResourceString(RB_BYO, "Order_By_Number"))
                            </a>
                            <span class="hidden-xs">
                                @Html.Raw(ResourceHelper.GetResourceString(RB_BYO, "Order_By_Number"))
                            </span>*@
                        <a id="order-whi" class="btn btn-default non-click-default-pointer  rsx-valign-middle rsx-bn-label order-whi" style="letter-spacing: normal" href="javascript:void(0);">
                            @Html.Raw(ResourceHelper.GetResourceString(RB_BunGlobal, "Order_now"))
                        </a>
                    </span>
                </div>
            </div>
        </div>
    </div>
    @if (prequal)
    {
        <div class="container margin-t-15">
            <button id="centralQualButton2" aria-labelledby="dynamicResultUpdate" class="btn btn-default" data-omni-s_oapt="104-0-0" data-omni-s_oprm="Check availability" data-omni-s_olbc="New client" data-omni-s_obrsqualtype="Top" onclick="openLightBox('eShopAddressModal1');getclikbuttonid();reloadFrame();return false;" tabindex="0" role="button" onkeydown="if(event.keyCode==32 || event.keyCode==13){event.preventDefault();$('#check_avl').click()};">
                <span id="check_avl">  @Html.Raw(ResourceHelper.GetResourceString(resourceCentralQual, "modal_title"))</span>
            </button>
        </div>
    }

    @*
TV STREAMING MARKETTING BANNER    
<div class="container">
        <div class="katsumi-tv-streaming-banner borderGrayLight6 borderRadiusAll10 d-flex margin-v-45 pad-45 pad-v-30-xs pad-h-xs-15">
        <div class="katsumi-tv-streaming-content">
            <h2 class="bellSlimBlack margin-b-15 txtBlack2 txtSize24">Save on all the streaming apps you like by subscribing with Bell</h2>
            <div role="list">
                <div role="listitem" class="d-flex">
                    <i class="icon-check-light icon3 mt-1 txtBlue txtSize12" aria-hidden="true"></i>
                    <div class="margin-l-10">
                        <p class="margin-0 txtBlack2 txtBold">Stream more, for less</p>
                        <p>Save when you subscribe to a bundle.</p>
                    </div>
                </div>
                <div role="listitem" class="d-flex">
                    <i class="icon-check-light icon3 mt-1 txtBlue txtSize12" aria-hidden="true"></i>
                    <div class="margin-l-10">
                        <p class="margin-0 txtBlack2 txtBold">Easy to manage, all on one bill</p>
                        <p>See everything in a single view, pay for all your streaming in one place, on the same date.</p>
                    </div>
                </div><div role="listitem" class="d-flex">
                    <i class="icon-check-light icon3 mt-1 txtBlue txtSize12" aria-hidden="true"></i>
                    <div class="margin-l-10">
                        <p class="margin-0 txtBlack2 txtBold">Transfer your existing subscriptions</p>
                        <p>No need to resubscribe or lose your history – just use the same email address when activating through MyBell.</p>
                    </div>
                </div>
            </div>
            <div>
                <a class="margin-l-20 txtUnderlineOnHover d-inline-flex" href="#">
                    <span class="margin-r-10" aria-hidden="true">@ResourceHelper.GetResourceString(resourceCentralQualStreaming, "faq_message")</span>
                    <span class="margin-r-10 sr-only">@ResourceHelper.GetResourceString(resourceCentralQualStreaming, "faq_message_SR")</span>
                    <span aria-hidden="true" class="txtSize8 icon icon-chevron-bold"></span>
                </a>
            </div>
        </div>
        @*this will be controlled by cms on integration story  -->
        <img aria-hidden="true" alt="" class="lazy" src="/Styles/BRF3/content/img/Katsumi/streaming_banner.png"/>
    </div>*@

</div>

<div id="modal_bundles_pkg_select_btn_buildYourOwn" class="modal fade scrollable-body overflow-modal bundle-packages-modal">
    <div class="modal-dialog modal-size-medium">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-customer-type-title" class="small-title pad-r-15" data-omni-content="@T("modal_title_order_now", "/BRSeShop/qualification/central_bundle", "en")"> @MvcHtmlString.Create(HttpUtility.HtmlDecode(ResourceHelper.GetResourceString("/BRSeShop/qualification/central_bundle", "modal_title_order_now")))</h2>
                <button type="button" class="close" data-dismiss="modal" aria-label="close"><span class="icon icon-close txtBlue txtSize20"></span></button>
            </div>
            <div class="modal-body">
                <div class="d-flex flex-column flex-sm-row">
                    <!-- Left Side of the Lightbox Order Now -->
                    @if (!isEmptyBUPEnabled || !pageContext.IsAuthenticated || isEmptyBUP)
                    {
                        <div class="@(pageContext.IsAuthenticated && isEmptyBUPEnabled && isEmptyBUP ? "" : "width-50 width-xs-100 flex-fill pad-b-xs-30")">
                            <div class="pad-b-15 pad-xs-5" data-omni-content="@T("new_to_bell_txt", "/BRSeShop/qualification/central", "en")">
                                @MvcHtmlString.Create(HttpUtility.HtmlDecode(ResourceHelper.GetResourceString("/BRSeShop/qualification/central", "new_to_bell_txt")))
                            </div>
                            <div class="">
                                <a id="bundles_pkg_select_btn_buildYourOwn" data-trackingCode="@ResourceHelper.GetResourceString("/bundles/ordernow_trackingcodes", "trackingcode_BYOB")" class="btn btn-default" href="javascript:void(0)" onclick="BuildYourOwn.GetSelectedBuildYourOwn(this);">
                                    @Html.Raw(ResourceHelper.GetResourceString(RB_BunGlobal, "Order_now"))
                                </a>
                            </div>
                        </div>
                    }
                    @{
                        string bellcahost = String.Format("{0}", System.Web.HttpContext.Current.Request.Url.Authority);
                        var urlRefer = HttpContext.Current.Request.RawUrl;
                    }
                    <!-- Right Side of the Lightbox Order Now -->
                    @if (!isEmptyBUPEnabled || !pageContext.IsAuthenticated || !isEmptyBUP)
                    {
                        <div class="@(pageContext.IsAuthenticated && isEmptyBUPEnabled && !isEmptyBUP ? "" : "flex-fill width-xs-100 width-50 pad-t-xs-30 pad-l-20 pad-l-xs-0 responsive-border-sm-light-grey")">
                            <div id="showCallinText" style="display:none;">@MvcHtmlString.Create(HttpUtility.HtmlDecode(ResourceHelper.GetResourceString("/BRSeShop/qualification/central", "existing_bell_customer_callus_txt")))</div>
                            <div id="showServiceButton" data-omni-content="@T("existing_bell_customer_txt", "/BRSeShop/qualification/central", "en")">
                                @MvcHtmlString.Create(HttpUtility.HtmlDecode(ResourceHelper.GetResourceString("/BRSeShop/qualification/central", "existing_bell_customer_txt")))
                                <div>
                                    <a id="add_lblfs" class="btn btn-primary qual-button js-omni-button btn btn-default  margin-t-xs-10 margin-l-xs-0" data-omni-s_oapt="647-0-0" data-omni-s_obtn="add_label" data-omni-s_obrsqualtype="bottom" href="@(Html.ReplaceHosts(ResourceHelper.GetResourceString("/BRSeShop/qualification/central", "dummy_flow_Login_url")) + bellcahost + ResourceHelper.GetResourceString("/BRSeShop/qualification/central", "dummy_flow_url") + "%3FUrlReferrer=" + urlRefer )">
                                        <span class="anchor-text">
                                            @ResourceHelper.GetResourceString("/BRSeShop/qualification/central_bundle", "add_label")
                                        </span>
                                    </a>
                                </div>
                                <div class="pad-t-20 pad-t-xs-10">
                                    <a id="manage_tvlbl" class="btn btn-primary qual-button  js-omni-button  margin-t-xs-10 margin-l-xs-0 margin-b-xs-10 margin-b-sm-0" data-omni-s_oapt="647-0-0" data-omni-s_obtn="modify label" data-omni-s_obrsqualtype="bottom" href="@Html.ReplaceHosts(ResourceHelper.GetResourceString("/BRSeShop/qualification/central_bundle", "modify_link"))">
                                        <span class="anchor-text">
                                            @ResourceHelper.GetResourceString("/BRSeShop/qualification/central_bundle", "modify_label")
                                        </span>
                                    </a>
                                    <br class="d-sm-none">
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
<input id="ajaxFailed" name="ajaxFailed" type="hidden" value="[{" ErrorCode":"ajax_error","ErrorType":"T","ErrorLayer":"FE","ErrorDesc":"Ajax Call Failure","IsUnknownTechnicalError":false,"ERR_CLASS":"ajax_error:[T|FE]","ERR_DESC":"ajax_error:Ajax Call Failure"}]" />
<input id="WhiOnlineOrder" type="hidden" value="@BRSEshopUtility.DisplayToggledNodes(Bell.ca.Framework.Common.Constants.Toggles.WHI_OnlineOrdering).ToString().ToLower()" />
<script>
    $(document).ready(function () {
        $(".order-whi").click(function () {
            if ($('#internet').is(':checked')) {
                window.localStorage.setItem('internet_order_package_name', '@Html.Raw(ResourceHelper.GetResourceString(RB_BunGlobal, "LOB_WHI"))');
            }
            if ($('#smarthome').is(':checked')) {
                window.localStorage.setItem('sh_order_package_name', '@Html.Raw(ResourceHelper.GetResourceString(RB_BunGlobal, "LOB_SmartHome"))');
            }
            if ($('#homephone').is(':checked')) {
                window.localStorage.setItem('hp_order_package_name', '@Html.Raw(ResourceHelper.GetResourceString(RB_BunGlobal, "LOB_HP"))');
            }
            if ($('#FibeTV').is(':checked')) {
                window.localStorage.setItem('tv_order_package_name', '@Html.Raw(ResourceHelper.GetResourceString(RB_BunGlobal, "LOB_FibeTV"))');
            }
            if ($('#SatTV').is(':checked')) {
                window.localStorage.setItem('tv_order_package_name', '@Html.Raw(ResourceHelper.GetResourceString(RB_BunGlobal, "LOB_SatTV"))');
            }
            if ($('#mobility').is(':checked')) {
                window.localStorage.setItem('mobility_order_package_name', '@Html.Raw(ResourceHelper.GetResourceString(RB_BunGlobal, "LOB_Mobility"))');
            }
            var getloadertext1 = $("#divloadertext").text();
            $('body').loadingIndicator({ message: getloadertext1 });
            $('body').loadingIndicator('show');
            window.location.href = "@(Bell.ca.Framework.Common.PageContext.CurrentPageContext.Page.Language == Bell.ca.Framework.Common.Language.En ? "/CallBackForm" : "/Accueil/CallBackForm")";
        });

        //EmptyBUP Direct to solution builder button
        function updateBundleButtonState() {
            var internetChecked = $("#internet").prop("checked");
            var fibeTVChecked = $("#FibeTV").prop("checked");
            var homePhoneChecked = $("#homephone").prop("checked");
            var $bundleButton = $("#build-your-bundle-button-emptybup");

            if (internetChecked || fibeTVChecked || homePhoneChecked) {
                $bundleButton.removeClass("emptybup-disabled").removeAttr("disabled");
            } else {
                $bundleButton.addClass("emptybup-disabled").attr("disabled", "true");
            }
        }

        $("#internet, #FibeTV, #homephone").on("change", updateBundleButtonState);

        updateBundleButtonState();

        $("#build-your-bundle-button-emptybup").click(function (event) {
            if ($(this).hasClass("emptybup-disabled")) {
                event.preventDefault();
                return;
            }
            BuildYourOwn.GetSelectedBuildYourOwn(this);
        });
    });
</script>
<style>
    .tile-subtext {
        font-size: 14px;
        font-weight: 400;
        color: #555;
    }

    .pre-qual .tile-subtext {
        color: #babec2;
    }

    .bundle-packages-modal .modal-dialog {
        width: 670px;
    }
</style>

<svg tabindex="-1" focusable="false" aria-hidden="true" style="position: absolute; width: 0; height: 0; overflow: hidden;" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <symbol id="icon-mobility-byop" viewBox="0 0 60 60">
            <path d="M57.69 28.77c0-1.44-.67-2.72-1.72-3.55a4.51 4.51 0 0 0 1.72-3.55c0-2.49-2.02-4.51-4.51-4.51h-1.19V5.99c0-1.42-.57-2.78-1.57-3.79-1.01-1-2.37-1.56-3.79-1.56H24.74a5.35 5.35 0 0 0-5.35 5.35v8.08h-2.62c-7.99.03-14.45 6.5-14.46 14.48v4.47C2.33 41 8.79 47.45 16.76 47.48h2.59v6.53a5.35 5.35 0 0 0 5.35 5.35h21.9c1.42.01 2.79-.54 3.8-1.55 1.01-1 1.58-2.37 1.58-3.8v-6.53h1.19c2.49 0 4.51-2.02 4.51-4.51 0-1.44-.67-2.72-1.72-3.55 1.05-.83 1.72-2.11 1.72-3.55s-.67-2.72-1.72-3.55a4.488 4.488 0 0 0 1.73-3.55zm-38.34-7.75V45.4h-2.58c-6.83-.03-12.36-5.56-12.38-12.38v-4.47c.01-6.83 5.55-12.37 12.38-12.39h13.04c1.34 0 2.43 1.09 2.43 2.43s-1.09 2.43-2.43 2.43H19.35zM49.9 54.01a3.261 3.261 0 0 1-3.3 3.26H24.69a3.26 3.26 0 0 1-3.26-3.26V23.1h8.38a4.511 4.511 0 0 0 0-9.02h-8.33V5.99c0-1.8 1.46-3.26 3.26-3.26h21.89c.87 0 1.7.34 2.32.95.61.61.96 1.44.96 2.31v48.02zm3.28-8.61h-1.19v-4.86h1.19c1.34 0 2.43 1.09 2.43 2.43s-1.09 2.43-2.43 2.43zm0-7.1h-1.19v-4.86h1.19c1.34 0 2.43 1.09 2.43 2.43s-1.09 2.43-2.43 2.43zm0-7.1h-1.19v-4.86h1.19c1.34 0 2.43 1.09 2.43 2.43s-1.09 2.43-2.43 2.43zm0-7.1h-1.19v-4.86h1.19c1.34 0 2.43 1.09 2.43 2.43s-1.09 2.43-2.43 2.43z" class="st0" />
            <path d="M39.61 6.51h-7.88a1.04 1.04 0 1 0 0 2.08h7.88a1.04 1.04 0 1 0 0-2.08z" />
        </symbol>
    </defs>
</svg>
@if (Bell.ca.Framework.Common.Utility.BRSEshopUtility.DisplayRSXToggledNodes("BrsEshopBuyFlowAutomation", false))
{
    <script>
    var selectedLobList = [];
    var selectedLobs = "";
    var addServiceBaseUrl = "";
    function onAllLobCheckBoxClick(cb, cbId) {
        var $link = $('#add_lblfs');
        if (addServiceBaseUrl === "" || addServiceBaseUrl === null || addServiceBaseUrl === undefined || addServiceBaseUrl === 'undefined') {
           addServiceBaseUrl = '@add_Existing_link';
            console.log(decodeURIComponent(addServiceBaseUrl));
        }
        if (cbId !== '' && cbId !== null && cbId !== undefined && cbId !== 'undefined') {
            if (cb.checked && !selectedLobList.includes(cbId)) {
                if ((cbId === "FIBE_TV") && !selectedLobList.includes("SHOP_INTERNET")) {
                    selectedLobList.push("SHOP_INTERNET");
                }
                selectedLobList.push(cbId);
            } else if (!cb.checked && selectedLobList.includes(cbId)) {
                selectedLobList.splice(selectedLobList.indexOf(cbId), 1);
            }
        }
        if (selectedLobList !== null && selectedLobList !== undefined && selectedLobList.length > 0) {
            selectedLobs = selectedLobList.join('|');
            console.log("%26LobType=" + selectedLobs);
            var newUrl = addServiceBaseUrl + "%26LobType=" + selectedLobs;
            $link.attr("href", newUrl);
        }
        else {
            selectedLobs = '';
            console.log("%26LobType=" + selectedLobs);
            var newUrl = addServiceBaseUrl;
            $link.attr("href", newUrl);
        }
        console.log($link.attr("href"));
    }
    </script>

}

<style>
    @@media (min-width:768px) {
        .information-byod {
            width: 450px;
        }
    }

    .tile-row.byo {
        padding-top: 45px;
        padding-bottom: 30px;
    }
</style>
@helper T(string resourceKey, string resourceFile, string lang)
{
    string cultureName = (!string.IsNullOrEmpty(lang) && lang == "fr") ? "fr-ca" : "en-ca";

    CultureInfo english = new CultureInfo(cultureName);
    @Html.Raw(Regex.Replace(ResourceHelper.GetResourceString(resourceFile, resourceKey, english), "<.*?>", String.Empty));
}
