@using Bell.ca.Models.Common;
@{
    string servertype = "";
    bool isDataPrivacyBannerEnabled = false;
    string OTdomainScript = "";
    try
    {
        servertype = System.Configuration.ConfigurationManager.AppSettings["servertype"];
        isDataPrivacyBannerEnabled = Bell.ca.Framework.Common.Utility.BRSEshopUtility.DisplayToggledNodes(Bell.ca.Framework.Common.Constants.Toggles.DataPrivacyCookiesToggle);
        OTdomainScript = (!string.IsNullOrEmpty(servertype)) && servertype.Equals("live", StringComparison.InvariantCultureIgnoreCase) ?
        Bell.ca.Framework.Common.Utility.ResourceHelper.GetResourceString("/global/SiteConfig", "OTSCRIPT_PROD") :
            Bell.ca.Framework.Common.Utility.ResourceHelper.GetResourceString("/global/SiteConfig", "OTSCRIPT_PROD");
    } catch(Exception e)
    {
        Bell.ca.Framework.Common.Log.Write(e, Bell.ca.Framework.Common.LogType.Trace);
    }

    <!--For Dev, use key -> OTSCRIPT_DEV, For PROD, use key -> OTSCRIPT_PROD -->

    <!--Commenting hardcoded string, pls remove it later -->
    //"16cc4bef-039e-4015-b3dc-05552d35810b" : "16cc4bef-039e-4015-b3dc-05552d35810b";

    /*
     * DO NOT REMOVE COMMENTS BELOW:
     *  -> _OneTrustDataPrivacy.cshtml-- START
     *  -> _OneTrustDataPrivacy.cshtml-- END
     *
     *  This is required in OneTrustActionFilter.cs
    */
    }

    <!-- _OneTrustDataPrivacy.cshtml -- START -->

@if (isDataPrivacyBannerEnabled)
{
    <!-- OneTrust Cookies Consent Notice start for bell.ca -->
    <script src="https://cdn.cookielaw.org/scripttemplates/otSDKStub.js" data-document-language="true" type="text/javascript" charset="UTF-8" data-domain-script="@OTdomainScript"></script>
    <script type="text/javascript">
        function OptanonWrapper() {
            $('.save-preference-btn-handler').click(function () {
                window.location.reload();
            });
        }
    </script>
 <!-- OneTrust Cookies Consent Notice end for bell.ca -->
    <link href="/Styles/CookieConsent/Bell/css/dataPrivacy.css" type="text/css" rel="stylesheet" />
    <script src="/Styles/CookieConsent/Bell/js/dataPrivacy.js"></script>
}
else
{
    <script>
        (function () {
            function processScripts() {
                // Select all script elements with type="text/plain"
                var scripts = document.querySelectorAll('script[type="text/plain"]');

                // Loop through the selected script elements
                scripts.forEach(function (script) {

                    // Check if the script element has a class that starts with "optanon-category"
                    var hasOptanonCategory = Array.from(script.classList).some(cls => cls.startsWith("optanon-category"));

                    if (hasOptanonCategory) {
                        // Create a new script element
                        var newScript = document.createElement('script');
                        newScript.type = "text/javascript";

                        // Copy attributes to the new script element
                        if (script.hasAttribute('async')) {
                            newScript.async = script.async;
                        }
                        if (script.hasAttribute('id')) {
                            newScript.id = script.id;
                        }
                        if (script.hasAttribute('src')) {
                            newScript.src = script.src;
                        } else {
                            // If there's no src attribute, copy the inline script content
                            newScript.textContent = script.textContent;
                        }

                        // Replace the old script with the new one
                        script.parentNode.replaceChild(newScript, script);
                    }
                });
            }

            if (window.jQuery) {
                $(document).ready(function () {
                    processScripts();
                });
            } else {
                document.addEventListener('DOMContentLoaded', function () {
                    processScripts();
                });
            }
        })();
    </script>
}
    <!-- _OneTrustDataPrivacy.cshtml -- END -->